import { ApiResponse } from "../types/api";
import api from "./api";

// 首页数据统计接口类型定义
export interface DashboardStats {
  // 客户数据统计
  customers: {
    total: number;
    newThisMonth: number;
    pendingApproval: number;
    approved: number;
    rejected: number;
  };
  
  // 合同数据统计
  contracts: {
    total: number;
    totalAmount: number;
    active: number;
    expired: number;
    expiringSoon: number;
    thisMonthSigned: number;
  };
  
  // 订单数据统计
  orders: {
    total: number;
    totalAmount: number;
    active: number;
    completed: number;
    cancelled: number;
    thisMonthOrders: number;
    thisMonthAmount: number;
  };
  
  // 收款数据统计
  receipts: {
    totalFlow: number;
    recognized: number;
    pending: number;
    unrecognized: number;
    thisMonthFlow: number;
    thisMonthRecognized: number;
    recognitionRate: number;
  };
  
  // 账务调整数据统计
  adjustments: {
    pendingCount: number;
    approvedCount: number;
    rejectedCount: number;
    thisMonthAmount: number;
    thisMonthCount: number;
    totalAdjustmentAmount: number;
  };
  
  // 系统概览数据
  overview: {
    totalRevenue: number;
    monthlyGrowth: number;
    activeCustomers: number;
    pendingTasks: number;
  };
}

// 获取首页统计数据
export const getDashboardStats = async (): Promise<ApiResponse<DashboardStats>> => {
  try {
    const response = await api.get("/dashboard/stats");
    return response.data;
  } catch (error) {
    console.error("Failed to fetch dashboard stats:", error);
    // 返回模拟数据作为后备方案
    return {
      code: 200,
      message: "success",
      data: {
        customers: {
          total: 1248,
          newThisMonth: 32,
          pendingApproval: 8,
          approved: 1205,
          rejected: 35,
        },
        contracts: {
          total: 856,
          totalAmount: 12580000,
          active: 742,
          expired: 99,
          expiringSoon: 15,
          thisMonthSigned: 12,
        },
        orders: {
          total: 2341,
          totalAmount: 45680000,
          active: 1876,
          completed: 465,
          cancelled: 0,
          thisMonthOrders: 89,
          thisMonthAmount: 3250000,
        },
        receipts: {
          totalFlow: 38920000,
          recognized: 32150000,
          pending: 6770000,
          unrecognized: 0,
          thisMonthFlow: 4580000,
          thisMonthRecognized: 3890000,
          recognitionRate: 82.6,
        },
        adjustments: {
          pendingCount: 12,
          approvedCount: 45,
          rejectedCount: 3,
          thisMonthAmount: 125000,
          thisMonthCount: 8,
          totalAdjustmentAmount: 2580000,
        },
        overview: {
          totalRevenue: 45680000,
          monthlyGrowth: 12.5,
          activeCustomers: 1205,
          pendingTasks: 35,
        },
      },
    };
  }
};

// 获取客户统计数据
export const getCustomerStats = async (): Promise<ApiResponse<DashboardStats['customers']>> => {
  try {
    const response = await api.get("/dashboard/customers/stats");
    return response.data;
  } catch (error) {
    console.error("Failed to fetch customer stats:", error);
    throw error;
  }
};

// 获取合同统计数据
export const getContractStats = async (): Promise<ApiResponse<DashboardStats['contracts']>> => {
  try {
    const response = await api.get("/dashboard/contracts/stats");
    return response.data;
  } catch (error) {
    console.error("Failed to fetch contract stats:", error);
    throw error;
  }
};

// 获取订单统计数据
export const getOrderStats = async (): Promise<ApiResponse<DashboardStats['orders']>> => {
  try {
    const response = await api.get("/dashboard/orders/stats");
    return response.data;
  } catch (error) {
    console.error("Failed to fetch order stats:", error);
    throw error;
  }
};

// 获取收款统计数据
export const getReceiptStats = async (): Promise<ApiResponse<DashboardStats['receipts']>> => {
  try {
    const response = await api.get("/dashboard/receipts/stats");
    return response.data;
  } catch (error) {
    console.error("Failed to fetch receipt stats:", error);
    throw error;
  }
};

// 获取账务调整统计数据
export const getAdjustmentStats = async (): Promise<ApiResponse<DashboardStats['adjustments']>> => {
  try {
    const response = await api.get("/dashboard/adjustments/stats");
    return response.data;
  } catch (error) {
    console.error("Failed to fetch adjustment stats:", error);
    throw error;
  }
};

// 获取系统概览数据
export const getOverviewStats = async (): Promise<ApiResponse<DashboardStats['overview']>> => {
  try {
    const response = await api.get("/dashboard/overview/stats");
    return response.data;
  } catch (error) {
    console.error("Failed to fetch overview stats:", error);
    throw error;
  }
};

// 刷新所有统计数据
export const refreshDashboardStats = async (): Promise<ApiResponse<DashboardStats>> => {
  try {
    const response = await api.post("/dashboard/stats/refresh");
    return response.data;
  } catch (error) {
    console.error("Failed to refresh dashboard stats:", error);
    throw error;
  }
};

// 获取趋势数据（用于图表展示）
export interface TrendData {
  date: string;
  value: number;
  label: string;
}

export const getRevenueTrend = async (period: 'week' | 'month' | 'quarter' = 'month'): Promise<ApiResponse<TrendData[]>> => {
  try {
    const response = await api.get(`/dashboard/trends/revenue?period=${period}`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch revenue trend:", error);
    throw error;
  }
};

export const getCustomerTrend = async (period: 'week' | 'month' | 'quarter' = 'month'): Promise<ApiResponse<TrendData[]>> => {
  try {
    const response = await api.get(`/dashboard/trends/customers?period=${period}`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch customer trend:", error);
    throw error;
  }
};

export const getOrderTrend = async (period: 'week' | 'month' | 'quarter' = 'month'): Promise<ApiResponse<TrendData[]>> => {
  try {
    const response = await api.get(`/dashboard/trends/orders?period=${period}`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch order trend:", error);
    throw error;
  }
};

// 获取待办事项列表
export interface TodoItem {
  id: number;
  title: string;
  description: string;
  type: 'customer_approval' | 'contract_expiring' | 'payment_pending' | 'adjustment_pending';
  priority: 'high' | 'medium' | 'low';
  dueDate: string;
  route?: string;
}

export const getTodoList = async (): Promise<ApiResponse<TodoItem[]>> => {
  try {
    const response = await api.get("/dashboard/todos");
    return response.data;
  } catch (error) {
    console.error("Failed to fetch todo list:", error);
    throw error;
  }
};

// 获取最近活动列表
export interface ActivityItem {
  id: number;
  title: string;
  description: string;
  type: 'customer' | 'contract' | 'order' | 'payment' | 'adjustment';
  timestamp: string;
  user: string;
  route?: string;
}

export const getRecentActivities = async (limit: number = 10): Promise<ApiResponse<ActivityItem[]>> => {
  try {
    const response = await api.get(`/dashboard/activities?limit=${limit}`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch recent activities:", error);
    throw error;
  }
};
