<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { useUserStore } from "../stores/user";
import { formatCurrency } from "../utils/common";
import { getDashboardStats, type DashboardStats } from "../services/dashboard";

const router = useRouter();
const userStore = useUserStore();

// 数据状态
const loading = ref(true);
const dashboardData = ref<DashboardStats>({
  // 客户数据
  customers: {
    total: 0,
    newThisMonth: 0,
    pendingApproval: 0,
    approved: 0,
    rejected: 0,
  },
  // 合同数据
  contracts: {
    total: 0,
    totalAmount: 0,
    active: 0,
    expired: 0,
    expiringSoon: 0,
    thisMonthSigned: 0,
  },
  // 订单数据
  orders: {
    total: 0,
    totalAmount: 0,
    active: 0,
    completed: 0,
    cancelled: 0,
    thisMonthOrders: 0,
    thisMonthAmount: 0,
  },
  // 收款数据
  receipts: {
    totalFlow: 0,
    recognized: 0,
    pending: 0,
    unrecognized: 0,
    thisMonthFlow: 0,
    thisMonthRecognized: 0,
    recognitionRate: 0,
  },
  // 账务调整数据
  adjustments: {
    pendingCount: 0,
    approvedCount: 0,
    rejectedCount: 0,
    thisMonthAmount: 0,
    thisMonthCount: 0,
    totalAdjustmentAmount: 0,
  },
  // 系统概览数据
  overview: {
    totalRevenue: 0,
    monthlyGrowth: 0,
    activeCustomers: 0,
    pendingTasks: 0,
  },
});

// 快捷操作数据 - 转换为SpeedDial格式
const quickActions = ref([
  {
    label: "新增客户",
    icon: "pi pi-user-plus",
    command: () => navigateTo("/customer-management/customers-info"),
  },
  {
    label: "创建合同",
    icon: "pi pi-file-plus",
    command: () => navigateTo("/contract-management/contracts-info"),
  },
  {
    label: "新建订单",
    icon: "pi pi-shopping-cart",
    command: () => navigateTo("/order-management/orders-info"),
  },
  {
    label: "银行流水",
    icon: "pi pi-money-bill",
    command: () => navigateTo("/receipt-management/bank-flow"),
  },
]);

// 计算属性
const welcomeMessage = computed(() => {
  const hour = new Date().getHours();
  let greeting = "早上好";
  if (hour >= 12 && hour < 18) {
    greeting = "下午好";
  } else if (hour >= 18) {
    greeting = "晚上好";
  }
  return `${greeting}，${userStore.user?.username || "用户"}`;
});

const currentDate = computed(() => {
  return new Date().toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "long",
    day: "numeric",
    weekday: "long",
  });
});

// 加载数据
const loadDashboardData = async () => {
  loading.value = true;
  try {
    // 调用API获取数据
    const response = await getDashboardStats();
    if (response.code === 200) {
      dashboardData.value = response.data;
    }
  } catch (error) {
    console.error("Failed to load dashboard data:", error);
  } finally {
    loading.value = false;
  }
};

// 导航到指定页面
const navigateTo = (route: string) => {
  router.push(route);
};

onMounted(() => {
  loadDashboardData();
});
</script>

<template>
  <div class="home-container">
    <div class="card">
      <!-- 欢迎区域 -->
      <div class="welcome-section">
        <div class="welcome-content">
          <h1 class="welcome-title">{{ welcomeMessage }}</h1>
          <p class="welcome-date">{{ currentDate }}</p>
        </div>
        <div class="welcome-actions">
          <Button
            icon="pi pi-refresh"
            text
            rounded
            @click="loadDashboardData"
            :loading="loading"
            v-tooltip.left="'刷新数据'"
            class="refresh-btn"
          />
        </div>
      </div>

      <!-- 数据概览卡片 -->
      <div class="stats-grid" v-if="!loading">
        <!-- 客户统计 -->
        <Card class="stat-card customer-card">
          <template #header>
            <div class="card-header">
              <div class="card-icon customer-icon">
                <i class="pi pi-users"></i>
              </div>
              <h3>客户管理</h3>
            </div>
          </template>
          <template #content>
            <div class="stat-content">
              <div class="main-stat">
                <span class="stat-number">{{
                  dashboardData.customers.total.toLocaleString()
                }}</span>
                <span class="stat-label">总客户数</span>
              </div>
              <div class="sub-stats">
                <div class="sub-stat">
                  <span class="sub-number">{{
                    dashboardData.customers.newThisMonth
                  }}</span>
                  <span class="sub-label">本月新增</span>
                </div>
                <div class="sub-stat">
                  <span class="sub-number pending">{{
                    dashboardData.customers.pendingApproval
                  }}</span>
                  <span class="sub-label">待审批</span>
                </div>
              </div>
            </div>
          </template>
          <template #footer>
            <Button
              label="查看详情"
              text
              @click="navigateTo('/customer-management/customers-info')"
              class="card-action-btn"
            />
          </template>
        </Card>

        <!-- 合同统计 -->
        <Card class="stat-card contract-card">
          <template #header>
            <div class="card-header">
              <div class="card-icon contract-icon">
                <i class="pi pi-file-edit"></i>
              </div>
              <h3>合同管理</h3>
            </div>
          </template>
          <template #content>
            <div class="stat-content">
              <div class="main-stat">
                <span class="stat-number">{{
                  dashboardData.contracts.total.toLocaleString()
                }}</span>
                <span class="stat-label">合同总数</span>
              </div>
              <div class="amount-stat">
                <span class="amount-number">{{
                  formatCurrency(dashboardData.contracts.totalAmount, "CNY")
                }}</span>
                <span class="amount-label">合同总金额</span>
              </div>
              <div class="sub-stats">
                <div class="sub-stat">
                  <span class="sub-number">{{
                    dashboardData.contracts.active
                  }}</span>
                  <span class="sub-label">生效中</span>
                </div>
                <div class="sub-stat">
                  <span class="sub-number warning">{{
                    dashboardData.contracts.expiringSoon
                  }}</span>
                  <span class="sub-label">即将到期</span>
                </div>
              </div>
            </div>
          </template>
          <template #footer>
            <Button
              label="查看详情"
              text
              @click="navigateTo('/contract-management/contracts-info')"
              class="card-action-btn"
            />
          </template>
        </Card>

        <!-- 订单统计 -->
        <Card class="stat-card order-card">
          <template #header>
            <div class="card-header">
              <div class="card-icon order-icon">
                <i class="pi pi-shopping-cart"></i>
              </div>
              <h3>订单管理</h3>
            </div>
          </template>
          <template #content>
            <div class="stat-content">
              <div class="main-stat">
                <span class="stat-number">{{
                  dashboardData.orders.total.toLocaleString()
                }}</span>
                <span class="stat-label">订单总数</span>
              </div>
              <div class="amount-stat">
                <span class="amount-number">{{
                  formatCurrency(dashboardData.orders.totalAmount, "CNY")
                }}</span>
                <span class="amount-label">订单总金额</span>
              </div>
              <div class="sub-stats">
                <div class="sub-stat">
                  <span class="sub-number">{{
                    dashboardData.orders.active
                  }}</span>
                  <span class="sub-label">进行中</span>
                </div>
                <div class="sub-stat">
                  <span class="sub-number success">{{
                    dashboardData.orders.completed
                  }}</span>
                  <span class="sub-label">已完成</span>
                </div>
              </div>
            </div>
          </template>
          <template #footer>
            <Button
              label="查看详情"
              text
              @click="navigateTo('/order-management/orders-info')"
              class="card-action-btn"
            />
          </template>
        </Card>

        <!-- 收款统计 -->
        <Card class="stat-card receipt-card">
          <template #header>
            <div class="card-header">
              <div class="card-icon receipt-icon">
                <i class="pi pi-money-bill"></i>
              </div>
              <h3>收款管理</h3>
            </div>
          </template>
          <template #content>
            <div class="stat-content">
              <div class="amount-stat">
                <span class="amount-number">{{
                  formatCurrency(dashboardData.receipts.totalFlow, "CNY")
                }}</span>
                <span class="amount-label">银行流水总额</span>
              </div>
              <div class="sub-stats">
                <div class="sub-stat">
                  <span class="sub-number success">{{
                    formatCurrency(dashboardData.receipts.recognized, "CNY")
                  }}</span>
                  <span class="sub-label">已认款</span>
                </div>
                <div class="sub-stat">
                  <span class="sub-number pending">{{
                    formatCurrency(dashboardData.receipts.pending, "CNY")
                  }}</span>
                  <span class="sub-label">待认款</span>
                </div>
              </div>
            </div>
          </template>
          <template #footer>
            <Button
              label="查看详情"
              text
              @click="navigateTo('/receipt-management/bank-flow')"
              class="card-action-btn"
            />
          </template>
        </Card>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <ProgressSpinner />
        <p>正在加载数据...</p>
      </div>

      <!-- SpeedDial 快捷操作 - 固定在右下角 -->
      <SpeedDial
        :model="quickActions"
        direction="up"
        type="circle"
        :radius="100"
        :style="{
          position: 'fixed',
          right: '20rem',
          bottom: '50rem',
          zIndex: 1000,
        }"
        buttonClass="p-button-help"
        :hideOnClickOutside="true"
        showIcon="pi pi-plus"
        hideIcon="pi pi-times"
        v-if="!loading"
      />
    </div>
  </div>
</template>

<style scoped>
.home-container {
  padding: 1rem;
  height: calc(100vh - 10rem);
  overflow-y: auto;
  /* 防止小屏幕水平溢出 */
  overflow-x: hidden;
  max-width: 100%;
  box-sizing: border-box;
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
  /* 防止内容溢出 */
  max-width: 100%;
  box-sizing: border-box;
}

/* 欢迎区域 */
.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem 0;
}

.welcome-content h1.welcome-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.02em;
}

.welcome-content p.welcome-date {
  font-size: 1.1rem;
  color: #86868b;
  margin: 0;
  font-weight: 400;
}

.welcome-actions .refresh-btn {
  color: #007aff;
  font-size: 1.2rem;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
  /* 防止小屏幕溢出 */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.stat-card {
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  /* 确保所有卡片高度一致 */
  display: flex;
  flex-direction: column;
  min-height: 280px;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 1.5rem 0 1.5rem;
}

.card-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.customer-icon {
  background: linear-gradient(135deg, #007aff, #5856d6);
}
.contract-icon {
  background: linear-gradient(135deg, #34c759, #30d158);
}
.order-icon {
  background: linear-gradient(135deg, #ff9500, #ff6b35);
}
.receipt-icon {
  background: linear-gradient(135deg, #5856d6, #af52de);
}

/* 统计内容 */
.stat-content {
  padding: 1rem 1.5rem;
  /* 让内容区域占据剩余空间，将footer推到底部 */
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.main-stat {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1d1d1f;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.stat-label {
  font-size: 0.95rem;
  color: #86868b;
  font-weight: 500;
  margin-top: 0.25rem;
}

.amount-stat {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.amount-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1d1d1f;
  line-height: 1.2;
}

.amount-label {
  font-size: 0.9rem;
  color: #86868b;
  font-weight: 500;
  margin-top: 0.25rem;
}

.sub-stats {
  display: flex;
  gap: 1.5rem;
}

.sub-stat {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.sub-number {
  font-size: 1.4rem;
  font-weight: 600;
  color: #1d1d1f;
  line-height: 1.2;
}

.sub-number.success {
  color: #34c759;
}
.sub-number.warning {
  color: #ff9500;
}
.sub-number.pending {
  color: #ff6b35;
}

.sub-label {
  font-size: 0.85rem;
  color: #86868b;
  font-weight: 500;
  margin-top: 0.25rem;
}

/* 卡片底部按钮 */
.card-action-btn {
  color: #007aff;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.card-action-btn:hover {
  background-color: rgba(0, 122, 255, 0.1);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 0;
  color: #86868b;
}

.loading-container p {
  margin-top: 1rem;
  font-size: 1.1rem;
}

/* SpeedDial 样式自定义 */
:deep(.p-speeddial-button) {
  width: 3.5rem !important;
  height: 3.5rem !important;
  background: linear-gradient(135deg, #34c759, #30d158) !important;
  border: none !important;
  box-shadow: 0 4px 20px rgba(52, 199, 89, 0.4) !important;
}

:deep(.p-speeddial-button:hover) {
  background: linear-gradient(135deg, #30d158, #34c759) !important;
  transform: scale(1.05) !important;
}

:deep(.p-speeddial-action) {
  width: 3rem !important;
  height: 3rem !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: #1d1d1f !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15) !important;
}

:deep(.p-speeddial-action:hover) {
  background: #34c759 !important;
  color: white !important;
  transform: scale(1.1) !important;
}

/* 响应式设计 */
/* 超大屏幕优化 */
@media (min-width: 1600px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .quick-actions-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* 大屏幕优化 */
@media (min-width: 1200px) and (max-width: 1599px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .quick-actions-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 平板设备优化 */
@media (min-width: 769px) and (max-width: 1199px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .quick-actions-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .welcome-content h1.welcome-title {
    font-size: 2.2rem;
  }
}

/* 小平板和大手机 */
@media (max-width: 768px) {
  .home-container {
    padding: 0.75rem;
  }

  .card {
    padding: 1rem;
  }

  .welcome-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 0;
  }

  .welcome-content h1.welcome-title {
    font-size: 2rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    min-height: 240px;
  }

  .card-header {
    padding: 1rem 1rem 0 1rem;
  }

  .stat-content {
    padding: 0.75rem 1rem;
  }
}

/* 手机设备 */
@media (max-width: 480px) {
  .home-container {
    padding: 0.5rem;
  }

  .card {
    padding: 0.75rem;
  }

  .welcome-content h1.welcome-title {
    font-size: 1.8rem;
  }

  .welcome-content p.welcome-date {
    font-size: 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .amount-number {
    font-size: 1.5rem;
  }

  .sub-number {
    font-size: 1.2rem;
  }

  .sub-stats {
    gap: 1rem;
  }

  .stat-card {
    min-height: 220px;
  }

  .stats-grid {
    gap: 0.75rem;
  }
}

/* 超小屏幕设备 */
@media (max-width: 360px) {
  .home-container {
    padding: 0.25rem;
  }

  .card {
    padding: 0.5rem;
  }

  .welcome-content h1.welcome-title {
    font-size: 1.6rem;
  }

  .stat-number {
    font-size: 1.8rem;
  }

  .amount-number {
    font-size: 1.3rem;
  }

  .card-header h3 {
    font-size: 1.1rem;
  }

  .stat-card {
    min-height: 200px;
  }

  .stats-grid {
    gap: 0.5rem;
    grid-template-columns: 1fr;
  }
}

/* PrimeVue组件样式覆盖 */
.stat-card :deep(.p-card-header) {
  padding: 0;
  border-bottom: none;
}

.stat-card :deep(.p-card-content) {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stat-card :deep(.p-card-footer) {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  margin-top: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.stat-card :deep(.p-card-body) {
  display: flex;
  flex-direction: column;
  height: 100%;
}
</style>
