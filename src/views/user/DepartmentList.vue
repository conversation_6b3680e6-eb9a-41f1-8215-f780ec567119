<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useToast } from "primevue/usetoast";
import {
  createDepartment,
  getDepartments,
  updateDepartment,
} from "../../services/department";
import {
  createUser,
  getDepartmentUsers,
  updateUser,
  UpdateUserRequest,
} from "../../services/user";
import { getRoleSimple } from "../../services/role";
import type { Department } from "../../types/department";
import type { User } from "../../types/user";
import type { RoleSimple } from "../../services/role";
import { userStatusMap, userStatusSeverityMap } from "../../utils/const";
import { Fluid } from "primevue";

const departments = ref<Department[]>([]);
const selectedDepartment = ref<{ key: string; label: string } | null>(null);
const selectedValue = ref<{ key: string; label: string } | null>(null);
const users = ref<User[]>([]);
const loading = ref(false);
const userLoading = ref(false);
const toast = useToast();
const treeNodes = ref<{ key: string; label: string; children?: any[] }[]>([]);
const selectedKey = ref<{ [key: string]: boolean }>({});
const roles = ref<RoleSimple[]>([]);

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 部门筛选参数
const departmentFilterValue = ref("");
const totalRecords = ref(0);

// 用户筛选参数
const userFilterValue = ref("");

// 加载部门数据
const loadDepartments = async () => {
  try {
    loading.value = true;
    const response = await getDepartments();
    departments.value = response.data;
    treeNodes.value = convertToTreeNodes(response.data);
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载部门数据失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 加载用户数据
const loadUsers = async (departmentId: number) => {
  try {
    userLoading.value = true;
    const params: any = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    // 如果有用户筛选值，传递word参数
    if (userFilterValue.value.trim()) {
      params.word = userFilterValue.value.trim();
    }

    const response = await getDepartmentUsers(departmentId, params);
    users.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载用户数据失败",
      life: 3000,
    });
  } finally {
    userLoading.value = false;
  }
};

// 处理部门选择
const onNodeSelect = (node: { key: string; label: string }) => {
  selectedDepartment.value = node;
  selectedKey.value = { [node.key]: true };
  lazyParams.value.page = 1;
  loadUsers(parseInt(node.key));
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  if (selectedDepartment.value) {
    loadUsers(parseInt(selectedDepartment.value.key));
  }
};

// 转换函数
const convertToTreeNodes = (
  data: Department[]
): { key: string; label: string; children?: any[] }[] => {
  if (!data || data.length === 0) {
    return [];
  }

  return data.map((node) => {
    const treeNode = {
      key: node.id.toString(),
      label: node.department_name,
      originalData: node,
    };

    if (node.children && node.children.length > 0) {
      Object.assign(treeNode, { children: convertToTreeNodes(node.children) });
    }

    return treeNode;
  });
};

// 加载角色数据
const loadRoles = async () => {
  try {
    const response = await getRoleSimple();
    roles.value = response.data;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载角色数据失败",
      life: 3000,
    });
  }
};

// 处理用户筛选
const handleUserFilter = () => {
  if (selectedDepartment.value) {
    lazyParams.value.page = 1;
    loadUsers(parseInt(selectedDepartment.value.key));
  }
};

// 重置用户筛选
const resetUserFilter = () => {
  userFilterValue.value = "";
  if (selectedDepartment.value) {
    lazyParams.value.page = 1;
    loadUsers(parseInt(selectedDepartment.value.key));
  }
};

// 搜索部门树节点
const searchDepartmentNode = (nodes: any[], searchValue: string): any => {
  for (const node of nodes) {
    if (node.label.toLowerCase().includes(searchValue.toLowerCase())) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = searchDepartmentNode(node.children, searchValue);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

// 处理部门筛选
const handleDepartmentFilter = () => {
  if (!departmentFilterValue.value.trim()) {
    return;
  }
  const foundNode = searchDepartmentNode(
    treeNodes.value,
    departmentFilterValue.value
  );
  if (foundNode) {
    selectedKey.value = { [foundNode.key]: true };
    selectedDepartment.value = foundNode;
    lazyParams.value.page = 1;
    loadUsers(parseInt(foundNode.key));
  } else {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "未找到匹配的部门",
      life: 3000,
    });
  }
};

// 重置部门筛选
const resetDepartmentFilter = () => {
  departmentFilterValue.value = "";
  selectedKey.value = {};
  selectedDepartment.value = null;
  users.value = [];
};

onMounted(() => {
  loadDepartments();
  loadRoles();
});

// 新增用户对话框控制
const userDrawer = ref(false);
const editMode = ref(false);
const userForm = ref<{
  id?: number;
  username: string;
  password: string;
  email: string;
  mobile: string;
  role_ids: number[];
}>({
  username: "",
  password: "",
  email: "",
  mobile: "",
  role_ids: [],
});
const submitted = ref(false);

// 打开新增用户对话框
const openNew = () => {
  userForm.value = {
    username: "",
    password: "",
    email: "",
    mobile: "",
    role_ids: [],
  };
  editMode.value = false;
  submitted.value = false;
  userDrawer.value = true;
};

// 打开编辑用户对话框
const openEdit = (user: User) => {
  userForm.value = {
    id: user.id,
    username: user.username,
    password: "", // 编辑时不显示密码
    email: user.email,
    mobile: user.mobile,
    role_ids: user.role_ids, // 暂时为空，需要从后端获取用户的角色列表
  };
  editMode.value = true;
  submitted.value = false;
  userDrawer.value = true;
};

// 隐藏对话框
const hideDialog = () => {
  userDrawer.value = false;
  submitted.value = false;
  editMode.value = false;
};

// 新增/修改部门相关状态
const departmentForm = ref<{
  id?: number;
  department_name: string;
  parent_id?: number | null;
}>({
  department_name: "",
  parent_id: null,
});
const departmentDrawerVisible = ref(false);
const fieldErrors = ref<{ [key: string]: string }>({});

// 打开添加部门抽屉
const openAddDepartment = (node?: any) => {
  departmentForm.value = {
    department_name: "",
    parent_id: node ? parseInt(node.key) : null,
  };
  fieldErrors.value = {};
  departmentDrawerVisible.value = true;
};

// 打开编辑部门抽屉
const openEditDepartment = (node?: any) => {
  departmentForm.value = {
    id: parseInt(node.key),
    department_name: node.label,
  };
  fieldErrors.value = {};
  departmentDrawerVisible.value = true;
};

// 提交新建部门
const saveDepartment = async () => {
  try {
    let response;
    if (departmentForm.value.id) {
      // 编辑角色时调用updateRole方法
      response = await updateDepartment(
        departmentForm.value,
        departmentForm.value.id
      );
    } else {
      // 新增角色时调用createRole方法
      response = await createDepartment(departmentForm.value);
    }
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: departmentForm.value.id ? "部门更新成功" : "部门创建成功",
        life: 3000,
      });
    }
    departmentDrawerVisible.value = false;
    loadDepartments(); // 刷新部门树
  } catch (error: any) {
    if (error.response.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(fieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: departmentForm.value.id ? "部门更新失败" : "部门创建失败",
        life: 3000,
      });
    }
  }
};

// 保存用户
const saveUser = async () => {
  submitted.value = true;
  try {
    const departmentId = selectedValue.value
      ? Object.keys(selectedValue.value)[0]
      : selectedDepartment.value
      ? selectedDepartment.value.key
      : "";
    if (!departmentId) {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: "请选择部门",
        life: 3000,
      });
      return;
    }
    let response;
    if (userForm.value.id) {
      // 编辑角色时调用updateRole方法
      const updateData: UpdateUserRequest = {
        username: userForm.value.username,
        email: userForm.value.email,
        mobile: userForm.value.mobile,
        role_ids: userForm.value.role_ids,
      };
      response = await updateUser(
        userForm.value.id,
        parseInt(departmentId),
        updateData
      );
    } else {
      // 新增角色时调用createRole方法
      response = await createUser(userForm.value, parseInt(departmentId));
    }
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: userForm.value.id ? "用户更新成功" : "用户创建成功",
        life: 3000,
      });
    }
    userDrawer.value = false;
    // 重新加载用户列表
    if (selectedDepartment.value) {
      loadUsers(parseInt(selectedDepartment.value.key));
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: editMode.value ? "更新用户失败" : "创建用户失败",
      life: 3000,
    });
  }
};
</script>

<template>
  <div class="department-list-container">
    <Toast />
    <div class="card">
      <div class="department-content">
        <!-- 左侧部门树 -->
        <div class="department-tree">
          <!-- 部门筛选区域 -->
          <Toolbar class="mb-2">
            <template #start>
              <Message icon="pi pi-sitemap" severity="success">部门</Message>
            </template>
            <template #end>
              <div class="flex flex-nowrap align-items-center gap-2">
                <FloatLabel>
                  <label>部门名称</label>
                  <InputText
                    v-model="departmentFilterValue"
                    class="flex-grow-1"
                  />
                </FloatLabel>
                <Button
                  icon="pi pi-search"
                  @click="handleDepartmentFilter"
                  outlined
                  rounded
                  class="flex-shrink-0"
                />
                <Button
                  icon="pi pi-refresh"
                  @click="resetDepartmentFilter"
                  outlined
                  rounded
                  class="flex-shrink-0"
                  severity="secondary"
                />
              </div>
              <Divider layout="vertical" />
              <Button
                label="新增"
                icon="pi pi-plus"
                @click="openAddDepartment"
              />
            </template>
          </Toolbar>
          <div class="tree-container">
            <Tree
              v-model:selectionKeys="selectedKey"
              :value="treeNodes"
              :loading="loading"
              @nodeSelect="onNodeSelect"
              selectionMode="single"
              class="w-full"
              :highlight-on-select="true"
            >
              <template #default="slotProps">
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: right;
                  "
                >
                  <span>{{ slotProps.node.label }}</span>
                  <div>
                    <Button
                      icon="pi pi-plus"
                      class="p-button-text p-button-sm"
                      @click.stop="openAddDepartment(slotProps.node)"
                    />
                    <Button
                      icon="pi pi-pencil"
                      class="p-button-text p-button-sm"
                      @click.stop="openEditDepartment(slotProps.node)"
                    />
                  </div>
                </div>
              </template>
            </Tree>
          </div>
        </div>
        <!-- 右侧用户列表 -->
        <div class="user-list">
          <div class="user-list-header">
            <Message icon="pi pi-users" severity="success">用户列表</Message>
          </div>
          <!-- 用户筛选区域 -->
           <Toolbar class="mb-2">
              <template #end>
                <div class="flex flex-nowrap align-items-center gap-2">
                  <FloatLabel>
                    <label>用户名称</label>
                    <InputText v-model="userFilterValue" class="flex-grow-1" />
                  </FloatLabel>
                  <Button
                    icon="pi pi-search"
                    @click="handleUserFilter"
                    rounded
                    class="flex-shrink-0"
                  />
                  <Button
                    icon="pi pi-refresh"
                    @click="resetUserFilter"
                    outlined
                    rounded
                    class="flex-shrink-0"
                    severity="secondary"
                  />
                </div>
                <Divider layout="vertical" />
                <Button
                  label="新增"
                  icon="pi pi-plus"
                  @click="openNew"
                />
              </template>
           </Toolbar>
          <DataTable
            :value="users"
            :lazy="true"
            :paginator="true"
            :rows="20"
            :rowsPerPageOptions="[10, 20, 50]"
            :totalRecords="totalRecords"
            :loading="userLoading"
            @page="onPage($event)"
            stripedRows
            showGridlines
            v-if="selectedDepartment"
            scrollable
            scrollHeight="calc(100vh - 25rem)"
          >
            <template #empty>
              <div class="empty-message">
                <i
                  class="pi pi-inbox"
                  style="
                    font-size: 2rem;
                    color: var(--p-text-color-secondary);
                    margin-bottom: 1rem;
                  "
                ></i>
                <p>暂无用户数据</p>
              </div>
            </template>
            <Column field="username" header="用户名" />
            <Column field="email" header="邮箱" />
            <Column field="mobile" header="手机号" />
            <Column field="roles" header="所属角色">
              <template #body="slotProps">
                <div
                  v-if="slotProps.data.roles && slotProps.data.roles.length > 0"
                >
                  <Tag
                    v-for="role in slotProps.data.roles"
                    :key="role.id"
                    :value="role.role_name"
                    severity="info"
                    class="mr-1"
                  />
                </div>
                <span v-else class="text-muted">暂无角色</span>
              </template>
            </Column>
            <Column field="state" header="状态">
              <template #body="slotProps">
                <Tag
                  :severity="
                    userStatusSeverityMap[slotProps.data.state] || 'info'
                  "
                  :value="userStatusMap[slotProps.data.state]"
                />
              </template>
            </Column>
            <Column header="操作" :exportable="false" style="min-width: 8rem">
              <template #body="slotProps">
                <Button
                  icon="pi pi-pencil"
                  outlined
                  rounded
                  class="mr-2"
                  @click="openEdit(slotProps.data)"
                />
              </template>
            </Column>
          </DataTable>
          <div class="no-department-selected" v-else>
            <i
              class="pi pi-info-circle"
              style="
                font-size: 2rem;
                color: var(--text-color-secondary);
                margin-bottom: 1rem;
              "
            ></i>
            <p>请选择左侧部门查看用户列表</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 新增/编辑用户抽屉 -->
    <Drawer
      v-model:visible="userDrawer"
      position="right"
      :style="{ width: '70rem' }"
      :modal="true"
      :closable="true"
      :dismissable="false"
      :showCloseIcon="true"
      :header="editMode ? '编辑用户' : '新增用户'"
      class="user-drawer p-fluid"
    >
    <div class="p-4">
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基本信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-2 gap-4">
              <div class="field">
                <label for="username" class="required">用户名</label>
                <InputText
                  id="username"
                  v-model="userForm.username"
                  :class="[{ 'p-invalid': submitted && !userForm.username }]"
                  placeholder="请输入用户名"
                />
                <small v-if="submitted && !userForm.username" class="p-error">
                  用户名不能为空
                </small>
              </div>
              <div class="field">
                <label for="password" :class="{ required: !editMode }">密码</label>
                <Password
                  id="password"
                  v-model="userForm.password"
                  :required="!editMode"
                  fluid
                  :class="{
                    'p-invalid': submitted && !userForm.password && !editMode,
                  }"
                  toggleMask
                  placeholder="请输入密码"
                />
                <small
                  v-if="submitted && !userForm.password && !editMode"
                  class="p-error"
                >
                  密码不能为空
                </small>
              </div>
              <div class="field">
                <label for="email" class="required">邮箱</label>
                <InputText
                  id="email"
                  v-model="userForm.email"
                  :class="[{ 'p-invalid': submitted && !userForm.email }]"
                  placeholder="请输入邮箱地址"
                />
                <small v-if="submitted && !userForm.email" class="p-error">
                  邮箱不能为空
                </small>
              </div>
              <div class="field">
                <label for="mobile" class="required">手机号</label>
                <InputText
                  id="mobile"
                  v-model="userForm.mobile"
                  :class="[{ 'p-invalid': submitted && !userForm.mobile }]"
                  placeholder="请输入手机号码"
                />
                <small v-if="submitted && !userForm.mobile" class="p-error">
                  手机号不能为空
                </small>
              </div>
            </div>
          </Fluid>
        </div>
      </div>
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">归属信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-2 gap-4">
              <div class="field">
                <label for="roles" class="required">角色</label>
                <MultiSelect
                  id="roles"
                  v-model="userForm.role_ids"
                  filter
                  fluid
                  display="chip"
                  :options="roles"
                  optionLabel="role_name"
                  optionValue="id"
                  placeholder="请选择角色"
                  :class="[{ 'p-invalid': submitted && !userForm.role_ids.length }]"
                />
                <small v-if="submitted && !userForm.role_ids.length" class="p-error">
                  请至少选择一个角色
                </small>
              </div>
              <div class="field" v-if="!selectedDepartment">
                <label for="department" class="required">部门</label>
                <TreeSelect
                  id="department_id"
                  :options="treeNodes"
                  optionValue="key"
                  :filter="true"
                  fluid
                  v-model="selectedValue"
                  selectionMode="single"
                  placeholder="请选择部门"
                  :class="[{ 'p-invalid': submitted && !selectedValue }]"
                  sm
                />
                <small v-if="submitted && !selectedValue" class="p-error">
                  请选择部门
                </small>
              </div>
            </div>
          </Fluid>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="取消"
          icon="pi pi-times"
          text
          @click="hideDialog"
          class="mr-2"
        />
        <Button 
          label="保存" 
          icon="pi pi-check" 
          @click="saveUser"
        />
      </div>
    </template>
    </Drawer>
    <!-- 新增/编辑部门抽屉 -->
    <Drawer
      v-model:visible="departmentDrawerVisible"
      position="right"
      :style="{ width: '70rem' }"
      :modal="true"
      :closable="true"
      :dismissable="false"
      :showCloseIcon="true"
      :header="departmentForm.id ? '编辑部门' : '新建部门'"
      class="department-drawer"
    >
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基本信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-1 gap-4">
              <div class="field">
                <label for="departmentName" class="required">
                  部门名称
                </label>
                <InputText
                  id="departmentName"
                  v-model="departmentForm.department_name"
                  :class="[{ 'p-invalid': fieldErrors.department_name }]"
                  placeholder="请输入部门名称"
                />
                <small v-if="fieldErrors.department_name" class="p-error">
                  {{ fieldErrors.department_name }}
                </small>
              </div>
            </div>
          </Fluid>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-2">
          <Button
            label="取消"
            icon="pi pi-times"
            text
            @click="departmentDrawerVisible = false"
            class="mr-2"
          />
          <Button
            label="保存"
            icon="pi pi-check"
            @click="saveDepartment"
          />
        </div>
      </template>
    </Drawer>
  </div>
</template>

<style scoped>
.department-list-container {
  padding: 1rem;
  height: calc(100vh - 10rem); /* 减去上下padding的高度 */
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.department-content {
  display: flex;
  gap: 1.5rem;
  flex: 1;
  min-height: 0; /* 防止flex子元素溢出 */
}

.department-tree {
  flex: 2;
  border: 1px solid var(--surface-border);
  border-radius: 10px;
  padding: 1rem;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
}

.user-list {
  flex: 8;
  border: 1px solid var(--surface-border);
  border-radius: 10px;
  padding: 1rem;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
}

h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--primary-color);
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--surface-border);
}

.tree-container {
  max-height: 70vh;
  overflow-y: auto;
  height: 90%;
  display: flex;
  flex-direction: column;
}

.no-department-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--surface-ground);
  border-radius: 6px;
  height: 300px;
}

.no-department-selected p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}

.user-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--surface-border);
}

.user-list-header h4 {
  margin: 0;
  padding: 0;
  border: none;
}

.field {
  margin-bottom: 0.5rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--p-primary-color);
  letter-spacing: -0.025em;
}

.field label.required::after {
  content: " *";
  color: #ff3b30;
  font-weight: 600;
  margin-left: 2px;
}

/* 表单验证错误样式 */
.p-error {
  color: #ff3b30;
  font-size: 0.75rem;
  font-weight: 400;
  margin-top: 0.25rem;
  display: block;
  line-height: 1.2;
}

:deep(.p-invalid) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 1px #ff3b30 !important;
}

:deep(.p-invalid:focus) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1) !important;
}

.p-fluid .p-button {
  width: auto;
}

.p-button-sm {
  padding: 0.25rem;
}

/* 抽屉样式 */
:deep(.user-drawer) {
  .p-drawer-content {
    display: flex;
    flex-direction: column;
  }
}

/* 表单分组样式 */
.form-section {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  margin-bottom: 1.5rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.section-header {
  background: var(--surface-ground);
  padding: 1rem 1.5rem 0.5rem;
}

.section-title {
  margin: 0 0 0.75rem 0;
  color: var(--text-color);
  font-weight: 600;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-section {
    margin-bottom: 1rem;
  }
  
  .section-content {
    padding: 1rem;
  }
}

/* MultiSelect 芯片样式 */
:deep(.p-multiselect .p-multiselect-token) {
  background: var(--primary-50);
  color: var(--primary-700);
  border-radius: 6px;
  padding: 0.25rem 0.5rem;
  margin: 0.125rem;
}

/* TreeSelect 样式 */
:deep(.p-treeselect .p-treeselect-label) {
  padding: 0.75rem;
}
</style>
